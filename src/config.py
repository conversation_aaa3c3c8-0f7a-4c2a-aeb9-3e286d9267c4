import os
from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict
from dotenv import load_dotenv

load_dotenv()


class Settings(BaseSettings):
    model_config = SettingsConfigDict(env_file=".env", case_sensitive=False)

    # Cal.com Configuration
    # API key is now optional since it can come from authorization headers
    calcom_api_key: str = ""
    calcom_base_url: str = "https://api.cal.com/v2"

    # Server Configuration
    mcp_server_name: str = "Cal.com MCP Server"
    mcp_server_port: int = 8000
    mcp_server_host: str = "0.0.0.0"

    # Logging
    log_level: str = "DEBUG"


settings = Settings()
