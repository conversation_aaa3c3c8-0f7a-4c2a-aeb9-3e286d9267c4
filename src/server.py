import logging
from datetime import datetime, timedelta, timezone
from typing import List, Dict, Any, Optional
import json
import contextlib
import uvicorn
import asyncio
from contextvars import ContextVar

from mcp.server.streamable_http_manager import StreamableHTTPSessionManager
from starlette.middleware.cors import CORSMiddleware
from starlette.applications import Starlette
from starlette.middleware import Middleware
from starlette.routing import Route
from mcp.server import Server
import mcp.types as types

from .constants.schema import *
from .constants.enum import Tools
from .config import settings
from .cal_client import CalcomClient
from .models import CreateBookingRequest, AvailabilityRequest

# Configure logging
logging.basicConfig(level=getattr(logging, settings.log_level))
logger = logging.getLogger(__name__)

# Initialize MCP server
server = Server("cal-com-mcp")

# Context variables to store request headers for authentication
auth_context: ContextVar[str] = ContextVar("auth_token")


def get_api_key_from_context() -> str:
    """
    Get the Cal.com API key from the authorization context.

    Returns:
        str: The API key extracted from the Authorization header

    Raises:
        Exception: If no API key is found in the context or settings
    """
    try:
        # Try to get API key from authorization header context
        api_key = auth_context.get()
        if api_key:
            return api_key
    except LookupError:
        pass

    # Fallback to settings if available
    if settings.calcom_api_key:
        return settings.calcom_api_key

    raise Exception(
        "Cal.com API key not found. Please provide it in the Authorization header as 'Bearer <your-api-key>' or set CALCOM_API_KEY environment variable."
    )


def check_and_raise_on_error(result: dict) -> dict:
    """
    Check if the Cal.com operation failed and raise an exception if it did.
    This ensures the MCP framework sets isError: true for failed operations.
    """
    if not result.get("success", True):
        # Extract error message from the result
        error_msg = result.get("error", "Unknown error occurred")
        raise Exception(error_msg)
    return result


@server.list_tools()
async def handle_list_tools() -> list[types.Tool]:
    """
    List available tools.
    Each tool specifies its arguments using JSON Schema validation.
    """
    return [
        # Booking management tools
        types.Tool(
            name=Tools.LIST_BOOKINGS,
            description="Get all Cal.com bookings for a specified time range. Returns a concise list with essential booking information.",
            inputSchema=ListBookings.model_json_schema(),
        ),
        types.Tool(
            name=Tools.CREATE_BOOKING,
            description="Create a new booking in Cal.com. Requires event type ID, start time, attendee details, and timezone.",
            inputSchema=CreateBooking.model_json_schema(),
        ),
        # types.Tool(
        #     name=Tools.GET_BOOKING,
        #     description="Get detailed information about a specific Cal.com booking by its UID",
        #     inputSchema=GetBooking.model_json_schema(),
        # ),
        # Event type tools
        types.Tool(
            name=Tools.LIST_EVENT_TYPES,
            description="Get all available Cal.com event types with their IDs, names, and durations",
            inputSchema=ListEventTypes.model_json_schema(),
        ),
        # types.Tool(
        #     name=Tools.GET_EVENT_TYPE,
        #     description="Get detailed information about a specific Cal.com event type",
        #     inputSchema=GetEventType.model_json_schema(),
        # ),
        # Availability tools
        types.Tool(
            name=Tools.CHECK_AVAILABILITY,
            description="Check available time slots for a Cal.com event type on a specific date",
            inputSchema=CheckAvailability.model_json_schema(),
        ),
    ]


@server.call_tool()
async def handle_call_tool(name: str, arguments: dict | None) -> types.CallToolResult:
    """
    Handle tool execution requests.
    Tools can modify server state and notify clients of changes.
    """
    try:
        match name:
            case Tools.LIST_BOOKINGS:
                result = await list_bookings_impl(
                    limit=arguments.get("limit", 100),
                    status=arguments.get("status"),
                    days_back=arguments.get("days_back", 30),
                    days_forward=arguments.get("days_forward", 30),
                )
                return [
                    types.TextContent(type="text", text=json.dumps(result, indent=2))
                ]

            case Tools.CREATE_BOOKING:
                result = await create_booking_impl(
                    event_type_id=arguments.get("event_type_id"),
                    start_time=arguments.get("start_time"),
                    attendee_name=arguments.get("attendee_name"),
                    attendee_email=arguments.get("attendee_email"),
                    timezone=arguments.get("timezone"),
                )
                return [
                    types.TextContent(type="text", text=json.dumps(result, indent=2))
                ]

            # case Tools.GET_BOOKING:
            #     result = await get_booking_impl(
            #         booking_uid=arguments.get("booking_uid")
            #     )
            #     return [
            #         types.TextContent(type="text", text=json.dumps(result, indent=2))
            #     ]

            case Tools.LIST_EVENT_TYPES:
                result = await list_event_types_impl()
                return [
                    types.TextContent(type="text", text=json.dumps(result, indent=2))
                ]

            # case Tools.GET_EVENT_TYPE:
            #     result = await get_event_type_impl(
            #         event_type_id=arguments.get("event_type_id")
            #     )
            #     return [
            #         types.TextContent(type="text", text=json.dumps(result, indent=2))
            #     ]

            case Tools.CHECK_AVAILABILITY:
                result = await check_availability_impl(
                    event_type_id=arguments.get("event_type_id"),
                    date=arguments.get("date"),
                    timezone=arguments.get("timezone"),
                )
                return [
                    types.TextContent(type="text", text=json.dumps(result, indent=2))
                ]

            case _:
                raise Exception(f"Invalid tool: {name}")

    except Exception as error:
        logger.error(f"Error in tool {name}: {error}")
        # Let the MCP framework handle the error response with isError: true
        raise error


# Tool implementation functions
async def list_bookings_impl(
    limit: int, status: Optional[str], days_back: int, days_forward: int
) -> Dict[str, Any]:
    """Implementation for listing Cal.com bookings"""
    try:
        # Calculate date range
        now = datetime.now(timezone.utc)
        start_date = now - timedelta(days=days_back)
        end_date = now + timedelta(days=days_forward)

        # Get API key from authorization context
        api_key = get_api_key_from_context()

        # Fetch bookings using Cal.com client
        async with CalcomClient(api_key=api_key) as cal_client:
            bookings_response = await cal_client.get_bookings(
                limit=limit,
                status=status,
                start_date=start_date,
                end_date=end_date,
            )

        bookings = bookings_response.bookings

        if not bookings:
            return {
                "success": True,
                "message": f"No bookings found in the past {days_back} days or next {days_forward} days.",
                "bookings": [],
                "total_count": 0,
            }

        # Convert bookings to dict format
        bookings_data = []
        for booking in bookings:
            bookings_data.append(
                {
                    "id": booking.id,
                    "title": booking.title,
                    "start_time": booking.start_time.isoformat(),
                    "end_time": booking.end_time.isoformat(),
                    "status": booking.status,
                    "uid": booking.uid,
                    "attendees": booking.attendees,
                    "location": booking.location,
                }
            )

        return {
            "success": True,
            "message": f"Found {len(bookings)} bookings",
            "bookings": bookings_data,
            "total_count": len(bookings),
            "date_range": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat(),
            },
        }

    except Exception as e:
        error_msg = f"Error fetching bookings: {str(e)}"
        logger.error(error_msg)
        return {"success": False, "error": error_msg, "bookings": [], "total_count": 0}


async def create_booking_impl(
    event_type_id: int,
    start_time: str,
    attendee_name: str,
    attendee_email: str,
    timezone: str,
) -> Dict[str, Any]:
    """Implementation for creating a Cal.com booking"""
    try:
        # Create booking request (end time will be calculated in the client)
        booking_request = CreateBookingRequest(
            event_type_id=event_type_id,
            start_time=start_time,
            attendee_name=attendee_name,
            attendee_email=attendee_email,
            timezone=timezone,
        )

        # Get API key from authorization context
        api_key = get_api_key_from_context()

        # Create booking using Cal.com client (it will handle end time calculation)
        async with CalcomClient(api_key=api_key) as cal_client:
            result = await cal_client.create_booking(booking_request)

        if result.success:
            return {
                "success": True,
                "message": f"Successfully created booking for {attendee_name} starting at {start_time}",
                "booking_id": result.booking_id,
                "booking_uid": result.booking_uid,
                "booking_url": result.booking_url,
                "booking": result.booking.model_dump() if result.booking else None,
            }
        else:
            return {
                "success": False,
                "error": result.message,
                "error_code": result.error_code,
                "error_details": result.error_details,
            }

    except Exception as e:
        error_msg = f"Error creating booking: {str(e)}"
        logger.error(error_msg)
        return {"success": False, "error": error_msg}


# async def get_booking_impl(booking_uid: str) -> Dict[str, Any]:
#     """Implementation for getting a specific booking by UID"""
#     try:
#         # Get API key from authorization context
#         api_key = get_api_key_from_context()
#
#         async with CalcomClient(api_key=api_key) as cal_client:
#             booking = await cal_client.get_booking_by_uid(booking_uid)

#         if booking:
#             return {
#                 "success": True,
#                 "message": f"Successfully fetched details for booking {booking_uid}",
#                 "booking": {
#                     "id": booking.id,
#                     "title": booking.title,
#                     "start_time": booking.start_time.isoformat(),
#                     "end_time": booking.end_time.isoformat(),
#                     "status": booking.status,
#                     "uid": booking.uid,
#                     "attendees": booking.attendees,
#                     "location": booking.location,
#                     "description": booking.description,
#                     "event_type_id": booking.event_type_id,
#                 },
#             }
#         else:
#             return {
#                 "success": False,
#                 "error": f"Booking with UID {booking_uid} not found",
#             }

#     except Exception as e:
#         error_msg = f"Failed to fetch booking details for {booking_uid}: {str(e)}"
#         logger.error(error_msg)
#         return {"success": False, "error": error_msg}


async def list_event_types_impl() -> Dict[str, Any]:
    """Implementation for listing Cal.com event types"""
    try:
        # Get API key from authorization context
        api_key = get_api_key_from_context()

        async with CalcomClient(api_key=api_key) as cal_client:
            event_types_response = await cal_client.get_event_types()

        event_types = event_types_response.event_types

        if not event_types:
            return {
                "success": True,
                "message": "No event types found.",
                "event_types": [],
                "total_count": 0,
            }

        # Convert event types to dict format - return all event types
        event_types_data = []
        for et in event_types:
            event_types_data.append(
                {
                    "id": et.id,
                    "title": et.title,
                    "slug": et.slug,
                    "length_minutes": et.length,
                    "description": et.description,
                    "locations": et.locations,
                }
            )

        return {
            "success": True,
            "message": f"Found {len(event_types_data)} event types",
            "event_types": event_types_data,
            "total_count": len(event_types_data),
        }

    except Exception as e:
        error_msg = f"Error fetching event types: {str(e)}"
        logger.error(error_msg)
        return {
            "success": False,
            "error": error_msg,
            "event_types": [],
            "total_count": 0,
        }


# async def get_event_type_impl(event_type_id: int) -> Dict[str, Any]:
#     """Implementation for getting a specific event type"""
#     try:
#         # Get API key from authorization context
#         api_key = get_api_key_from_context()
#
#         async with CalcomClient(api_key=api_key) as cal_client:
#             event_types_response = await cal_client.get_event_types()

#         event_types = event_types_response.event_types

#         # Find the specific event type
#         event_type = None
#         for et in event_types:
#             if et.id == event_type_id:
#                 event_type = et
#                 break

#         if not event_type:
#             return {
#                 "success": False,
#                 "error": f"Event type with ID {event_type_id} not found",
#             }

#         return {
#             "success": True,
#             "message": f"Successfully fetched event type {event_type_id}",
#             "event_type": {
#                 "id": event_type.id,
#                 "title": event_type.title,
#                 "slug": event_type.slug,
#                 "length": event_type.length,
#                 "description": event_type.description,
#                 "locations": event_type.locations,
#             },
#         }

#     except Exception as e:
#         error_msg = f"Error fetching event type {event_type_id}: {str(e)}"
#         logger.error(error_msg)
#         return {"success": False, "error": error_msg}


async def check_availability_impl(
    event_type_id: int, date: str, timezone: str
) -> Dict[str, Any]:
    """Implementation for checking availability for a Cal.com event type"""
    try:
        # Create availability request
        availability_request = AvailabilityRequest(
            event_type_id=event_type_id,
            date_from=date,
            timezone=timezone,
        )

        # Get API key from authorization context
        api_key = get_api_key_from_context()

        # Check availability using Cal.com client
        async with CalcomClient(api_key=api_key) as cal_client:
            availability = await cal_client.check_availability(availability_request)

        if not availability.success:
            return {"success": False, "error": availability.message}

        if availability.total_slots == 0:
            return {
                "success": True,
                "message": f"No available time slots found for event type {event_type_id} on {date}.",
                "available_times": [],
                "total_slots": 0,
                "date": date,
                "timezone": timezone,
            }

        # Simplify slots to just time strings for easier consumption
        available_times = []
        for slot in availability.available_slots:
            # Extract just the time part and format it nicely
            time_str = slot.time
            # Convert from full ISO format to simpler format
            try:
                dt = datetime.fromisoformat(time_str.replace("Z", "+00:00"))
                # Format as HH:MM for simplicity
                formatted_time = dt.strftime("%H:%M")
                available_times.append(formatted_time)
            except Exception:
                # Fallback to original time if parsing fails
                available_times.append(time_str)

        return {
            "success": True,
            "message": f"Found {availability.total_slots} available time slots on {date}",
            "available_times": available_times,
            "total_slots": availability.total_slots,
            "date": date,
            "timezone": timezone,
            "note": "Times are shown in the requested timezone",
        }

    except Exception as e:
        error_msg = f"Error checking availability: {str(e)}"
        logger.error(error_msg)
        return {"success": False, "error": error_msg}


# HTTP Session Manager and Server Setup
async def create_app():
    """Create the Starlette application with MCP session manager"""

    # Create the session manager with the server
    try:
        session_manager = StreamableHTTPSessionManager(
            app=server,
            json_response=False,  # Use SSE format for responses
            stateless=False,  # Stateful mode for better user experience
        )
        logger.info("StreamableHTTPSessionManager initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize StreamableHTTPSessionManager: {e}")
        session_manager = None

    # Create a class for handling streamable HTTP connections
    class HandleStreamableHttp:
        def __init__(self, session_manager):
            self.session_manager = session_manager

        def _extract_headers(self, scope):
            """Extract authorization headers from request scope."""
            headers = dict(scope.get("headers", []))

            # Extract Authorization header if needed for future auth
            auth_header = headers.get(b"authorization")
            if auth_header:
                auth_token = auth_header.decode("utf-8")
                auth_token = auth_token.replace("Bearer ", "")
                return auth_token
            return None

        async def __call__(self, scope, receive, send):
            if self.session_manager is not None:
                try:
                    logger.info("Handling Streamable HTTP connection...")

                    # Extract headers for future auth if needed
                    auth_token = self._extract_headers(scope)
                    if auth_token:
                        auth_context.set(auth_token)

                    await self.session_manager.handle_request(scope, receive, send)
                    logger.info("Streamable HTTP connection closed")
                except Exception as e:
                    logger.error(f"Error handling Streamable HTTP request: {e}")
                    await send(
                        {
                            "type": "http.response.start",
                            "status": 500,
                            "headers": [(b"content-type", b"application/json")],
                        }
                    )
                    await send(
                        {
                            "type": "http.response.body",
                            "body": json.dumps(
                                {"error": f"Internal server error: {str(e)}"}
                            ).encode("utf-8"),
                        }
                    )
            else:
                # Return a 501 Not Implemented response if streamable HTTP is not available
                await send(
                    {
                        "type": "http.response.start",
                        "status": 501,
                        "headers": [(b"content-type", b"application/json")],
                    }
                )
                await send(
                    {
                        "type": "http.response.body",
                        "body": json.dumps(
                            {"error": "Streamable HTTP transport is not available"}
                        ).encode("utf-8"),
                    }
                )

    # Define routes
    routes = []

    # Add Streamable HTTP route if available
    if session_manager is not None:
        routes.append(
            Route(
                "/mcp", endpoint=HandleStreamableHttp(session_manager), methods=["POST"]
            )
        )

    middleware = [
        Middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_methods=["*"],
            allow_headers=["*"],
        )
    ]

    # Define lifespan for session manager
    @contextlib.asynccontextmanager
    async def lifespan(app):
        """Context manager for session manager."""
        if session_manager is not None:
            async with session_manager.run():
                logger.info("Application started with StreamableHTTP session manager!")
                try:
                    yield
                finally:
                    logger.info("Application shutting down...")
        else:
            # No session manager, just yield
            yield

    return Starlette(routes=routes, middleware=middleware, lifespan=lifespan)


async def start_server():
    """Start the server asynchronously."""
    app = await create_app()
    host = getattr(settings, "host", "0.0.0.0")
    port = getattr(settings, "port", 8000)

    logger.info(f"Starting Cal.com MCP server at {host}:{port}")

    # Use uvicorn's async API
    config = uvicorn.Config(app, host=host, port=port)
    server_instance = uvicorn.Server(config)
    await server_instance.serve()


def main():
    """Main entry point for the server"""
    while True:
        try:
            # Use asyncio.run to run the async start_server function
            asyncio.run(start_server())
        except KeyboardInterrupt:
            logger.info("Server stopped by user")
            break
        except Exception as e:
            logger.error(f"Server crashed with error: {e}")
            continue


if __name__ == "__main__":
    main()
