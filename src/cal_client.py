import httpx
from typing import List, Optional, Dict, Any
from datetime import datetime
import logging
from .config import settings
from .models import (
    CalcomBooking,
    EventType,
    BookingListResponse,
    EventTypeListResponse,
    CreateBookingRequest,
    CreateBookingResponse,
    AvailabilitySlot,
    AvailabilityRequest,
    AvailabilityResponse,
)

logger = logging.getLogger(__name__)


class CalcomClient:
    """Client for interacting with Cal.com API"""

    def __init__(self):
        self.base_url = settings.calcom_base_url
        self.api_key = settings.calcom_api_key
        # Set up headers for v2 API authentication
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
        }
        self.client = httpx.AsyncClient(
            base_url=self.base_url, timeout=30.0, headers=headers
        )

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()

    def _get_params(self, additional_params: Dict[str, Any] = None) -> Dict[str, Any]:
        """Get parameters for API requests (v2 uses header auth, no apiKey needed)"""
        params = {}
        if additional_params:
            params.update(additional_params)
        return params

    def _extract_location(self, location_data: Any) -> Optional[str]:
        """Extract location string from Cal.com location data"""
        if not location_data:
            return None
        if isinstance(location_data, str):
            if location_data.strip() == "":
                return None
            return location_data
        if isinstance(location_data, dict):
            return location_data.get("value") or location_data.get("address")
        return str(location_data)

    def _parse_booking(self, booking: Dict[str, Any]) -> CalcomBooking:
        """Parses a booking dictionary into a CalcomBooking object."""
        # Handle different field names between v1 and v2 APIs
        start_time_str = booking.get("startTime") or booking.get("start")
        end_time_str = booking.get("endTime") or booking.get("end")

        # Handle event_type_id field name differences and nested structures
        event_type_id = booking.get("eventTypeId") or booking.get("event_type_id")
        if event_type_id is None:
            # Try to get from nested eventType object (v2 API sometimes nests this)
            event_type = booking.get("eventType", {})
            if isinstance(event_type, dict):
                event_type_id = event_type.get("id")

        # Ensure we have required fields
        if not start_time_str:
            raise ValueError(f"Missing start time in booking data: {booking}")
        if not end_time_str:
            raise ValueError(f"Missing end time in booking data: {booking}")
        if event_type_id is None:
            raise ValueError(f"Missing event type ID in booking data: {booking}")

        return CalcomBooking(
            id=booking.get("id"),
            title=booking.get("title", "Cal.com Booking"),
            description=booking.get("description"),
            start_time=datetime.fromisoformat(start_time_str.replace("Z", "+00:00")),
            end_time=datetime.fromisoformat(end_time_str.replace("Z", "+00:00")),
            attendees=booking.get("attendees", []),
            location=self._extract_location(booking.get("location")),
            event_type_id=event_type_id,
            status=booking.get("status", "confirmed"),
            uid=booking.get("uid", ""),
        )

    def _parse_event_type(self, et: Dict[str, Any]) -> EventType:
        """Parses an event type dictionary into an EventType object."""
        # Handle both v1 and v2 API field names
        return EventType(
            id=et.get("id"),
            title=et.get("title"),
            slug=et.get("slug"),
            length=et.get("length") or et.get("lengthInMinutes", 30),
            description=et.get("description"),
            locations=et.get("locations", []),
        )

    async def get_bookings(
        self,
        limit: int = 100,
        status: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        uid: Optional[str] = None,
    ) -> BookingListResponse:
        """Fetch bookings from Cal.com"""
        try:
            params = self._get_params({"take": limit})

            if status:
                params["status"] = status
            if start_date:
                params["startTime"] = start_date.isoformat()
            if end_date:
                params["endTime"] = end_date.isoformat()
            if uid:
                params["uid"] = uid

            response = await self.client.get("/bookings", params=params)
            response.raise_for_status()
            data = response.json()

            # Debug: Log the raw response to understand the structure
            logger.debug(f"Raw bookings API response: {data}")

            # Handle v2 API response format with status/data wrapper
            if data.get("status") == "success" and "data" in data:
                bookings_data = data["data"]
                if isinstance(bookings_data, list):
                    bookings = []
                    for b in bookings_data:
                        try:
                            bookings.append(self._parse_booking(b))
                        except ValueError as e:
                            logger.warning(f"Skipping invalid booking data: {e}")
                            logger.debug(f"Invalid booking data: {b}")
                else:
                    # Handle case where data is an object with bookings array
                    bookings = []
                    for b in bookings_data.get("bookings", []):
                        try:
                            bookings.append(self._parse_booking(b))
                        except ValueError as e:
                            logger.warning(f"Skipping invalid booking data: {e}")
                            logger.debug(f"Invalid booking data: {b}")
            else:
                # Fallback for v1 format or direct array
                bookings = []
                for b in data.get("bookings", []):
                    try:
                        bookings.append(self._parse_booking(b))
                    except ValueError as e:
                        logger.warning(f"Skipping invalid booking data: {e}")
                        logger.debug(f"Invalid booking data: {b}")

            return BookingListResponse(
                bookings=bookings,
                total_count=len(bookings),
                message=f"Retrieved {len(bookings)} bookings successfully",
            )

        except httpx.HTTPError as e:
            logger.error(f"Error fetching bookings: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error fetching bookings: {e}")
            raise

    async def get_booking_by_uid(self, uid: str) -> Optional[CalcomBooking]:
        """Fetch a single booking by its UID."""
        try:
            response = await self.get_bookings(limit=1, uid=uid)
            if response.bookings:
                return response.bookings[0]
            return None
        except Exception as e:
            logger.error(f"Error fetching booking by UID {uid}: {e}")
            raise

    async def create_booking(
        self, booking_request: CreateBookingRequest
    ) -> CreateBookingResponse:
        """Create a new booking in Cal.com"""
        try:
            # Get event type details to extract slug
            event_types_response = await self.get_event_types()
            event_type = next(
                (
                    et
                    for et in event_types_response.event_types
                    if et.id == booking_request.event_type_id
                ),
                None,
            )
            if not event_type:
                return CreateBookingResponse(
                    success=False,
                    message=f"Event type {booking_request.event_type_id} not found",
                    error_code="event_type_not_found",
                )

            # Convert start time from local timezone to UTC
            from datetime import datetime, timedelta
            from zoneinfo import ZoneInfo

            # Parse the start time as a naive datetime (assuming it's in the provided timezone)
            if booking_request.start_time.endswith("Z"):
                # If already in UTC format, parse directly
                start_dt_utc = datetime.fromisoformat(
                    booking_request.start_time.replace("Z", "+00:00")
                )
            else:
                # Parse as naive datetime and localize to the provided timezone
                start_dt_naive = datetime.fromisoformat(booking_request.start_time)

                # Create timezone-aware datetime in the provided timezone
                try:
                    local_tz = ZoneInfo(booking_request.timezone)
                    start_dt_local = start_dt_naive.replace(tzinfo=local_tz)
                    # Convert to UTC
                    start_dt_utc = start_dt_local.astimezone(ZoneInfo("UTC"))
                except Exception as e:
                    logger.error(
                        f"Error converting timezone {booking_request.timezone}: {e}"
                    )
                    # Fallback: assume the time is already in UTC
                    start_dt_utc = start_dt_naive.replace(tzinfo=ZoneInfo("UTC"))

            # Format times as ISO strings with Z suffix for UTC
            start_time_utc = start_dt_utc.isoformat().replace("+00:00", "Z")

            booking_data = {
                "eventTypeId": booking_request.event_type_id,
                "start": start_time_utc,  # Use UTC time
                "attendee": {
                    "name": booking_request.attendee_name,
                    "email": booking_request.attendee_email,
                    "timeZone": booking_request.timezone,
                    "language": "en",  # Simplified - always English
                },
                "metadata": {},  # Required field for Cal.com API
            }

            params = self._get_params()

            # Add cal-api-version header for v2 API
            headers = {"cal-api-version": "2024-08-13"}

            logger.info(f"Creating booking with data: {booking_data}")
            logger.info(f"Request params: {params}")

            response = await self.client.post(
                "/bookings", json=booking_data, params=params, headers=headers
            )

            # Handle both successful responses and responses with warnings
            result = response.json()
            logger.info(f"Booking creation response: {result}")

            # Handle v2 API response format with status/data wrapper
            if result.get("status") == "success" and "data" in result:
                booking_info = result["data"]
            else:
                # Fallback for v1 format
                booking_info = result.get("booking")

            # Check if booking was created successfully (even if there are warnings)
            if booking_info and booking_info.get("id"):
                video_url = booking_info.get("metadata", {}).get("videoCallUrl")
                if not video_url:
                    location = self._extract_location(booking_info.get("location"))
                    if location and location.startswith("https"):
                        video_url = location

                # Check for warnings in the response
                warning_message = None
                response_message = result.get("message", "")
                if (
                    "sharp" in response_message.lower()
                    and "module" in response_message.lower()
                ):
                    warning_message = "Booking created successfully, but there was a non-critical image processing warning. This does not affect your booking."

                success_message = (
                    f"Booking created successfully for {booking_request.attendee_name}"
                )
                if warning_message:
                    success_message += f" ({warning_message})"

                return CreateBookingResponse(
                    success=True,
                    booking_id=booking_info.get("id"),
                    booking_uid=booking_info.get("uid"),
                    booking_url=video_url,
                    message=success_message,
                )
            else:
                # Only raise for status if booking wasn't created
                response.raise_for_status()
                return CreateBookingResponse(
                    success=False,
                    message=f"Failed to create booking: {result.get('message', 'Unknown error from Cal.com API')}",
                )

        except httpx.HTTPStatusError as e:
            error_message = f"HTTP error creating booking: {e.response.status_code}"
            error_code = None
            error_details = {}

            try:
                error_body = e.response.json()
                api_error_message = error_body.get(
                    "message", "No error message in response"
                )
                error_code = error_body.get("error", error_body.get("code"))

                # Handle specific Cal.com errors
                if "no_available_users_found_error" in str(error_body).lower():
                    error_code = "no_available_users_found_error"
                    error_message = "No available time slots found for the requested time. Please check availability and try a different time slot."
                    error_details = {
                        "suggestion": "Use the check_calcom_availability tool to find available time slots",
                        "requested_time": f"{booking_request.start} to {booking_request.end}",
                        "event_type_id": booking_request.event_type_id,
                    }
                elif "event_type_not_found" in str(error_body).lower():
                    error_code = "event_type_not_found"
                    error_message = f"Event type with ID {booking_request.event_type_id} not found. Please check the event type ID."
                    error_details = {
                        "suggestion": "Use the get_calcom_event_types tool to find valid event type IDs",
                        "event_type_id": booking_request.event_type_id,
                    }
                elif e.response.status_code == 404 and (
                    "database" in api_error_message.lower()
                    or "querying" in api_error_message.lower()
                    or api_error_message
                    == "An error occurred while querying the database."
                ):
                    error_code = "event_type_not_found"
                    error_message = f"Event type with ID {booking_request.event_type_id} does not exist or is not accessible to your account."
                    error_details = {
                        "message": "The event type ID provided does not exist in your Cal.com account or you don't have permission to access it.",
                        "data": None,
                        "suggestion": "Use the get_calcom_event_types tool to find valid event type IDs for your account",
                        "event_type_id": booking_request.event_type_id,
                        "original_error": api_error_message,
                    }
                elif (
                    "sharp" in api_error_message.lower()
                    and "module" in api_error_message.lower()
                ):
                    # Handle sharp module errors as non-critical warnings - check this FIRST
                    # to prevent it from being caught by the time error detection
                    error_code = "image_processing_warning"
                    error_message = f"Booking may have been created, but there was an image processing warning: {api_error_message}"
                    error_details = {
                        "suggestion": "Check if the booking was actually created using get_calcom_bookings tool",
                        "warning_type": "sharp_module_error",
                        "original_error": api_error_message,
                    }
                elif (
                    "invalid_time" in str(error_body).lower()
                    or "invalid time" in api_error_message.lower()
                    or (
                        "time" in api_error_message.lower()
                        and any(
                            keyword in api_error_message.lower()
                            for keyword in [
                                "invalid",
                                "past",
                                "future",
                                "format",
                                "zone",
                            ]
                        )
                        and "sharp"
                        not in api_error_message.lower()  # Exclude sharp module errors
                        and "runtime"
                        not in api_error_message.lower()  # Exclude runtime errors from sharp
                    )
                ):
                    error_code = "invalid_time"
                    error_message = f"Invalid time specified: {api_error_message}"
                    error_details = {
                        "suggestion": "Ensure times are in the future and in valid ISO format",
                        "requested_start": booking_request.start,
                        "requested_end": booking_request.end,
                    }
                else:
                    error_message += f" - {api_error_message}"
                    error_details = error_body

            except Exception:
                # If we can't parse the error body, but it's a 404, assume it's an event type issue
                if e.response.status_code == 404:
                    error_code = "event_type_not_found"
                    error_message = f"Event type with ID {booking_request.event_type_id} does not exist or is not accessible to your account."
                    error_details = {
                        "message": "The event type ID provided does not exist in your Cal.com account or you don't have permission to access it.",
                        "data": None,
                        "suggestion": "Use the get_calcom_event_types tool to find valid event type IDs for your account",
                        "event_type_id": booking_request.event_type_id,
                        "original_error": e.response.text,
                    }
                else:
                    error_message += f" - {e.response.text}"

            logger.error(error_message)
            return CreateBookingResponse(
                success=False,
                message=error_message,
                error_code=error_code,
                error_details=error_details,
            )
        except httpx.HTTPError as e:
            logger.error(f"HTTP error creating booking: {e}")
            return CreateBookingResponse(
                success=False, message=f"Failed to create booking: {str(e)}"
            )
        except Exception as e:
            logger.error(f"Unexpected error creating booking: {e}")
            return CreateBookingResponse(
                success=False, message=f"Unexpected error: {str(e)}"
            )

    async def get_event_types(self) -> EventTypeListResponse:
        """Fetch available event types"""
        try:
            params = self._get_params()
            response = await self.client.get("/event-types", params=params)
            response.raise_for_status()
            data = response.json()

            # Debug: Log the raw response to understand the structure
            logger.debug(f"Raw event types API response: {data}")

            # Handle v2 API response format with status/data wrapper
            if data.get("status") == "success" and "data" in data:
                event_types_data = data["data"]
                event_types_list = []

                # v2 API: event types are nested in eventTypeGroups
                if "eventTypeGroups" in event_types_data:
                    for group in event_types_data["eventTypeGroups"]:
                        if isinstance(group, dict) and "eventTypes" in group:
                            event_types_list.extend(group["eventTypes"])
                elif isinstance(event_types_data, list):
                    # Direct array of event types
                    event_types_list = event_types_data
                else:
                    # Handle case where data is an object with event_types array
                    event_types_list = event_types_data.get("event_types", [])
            else:
                # Fallback for v1 format or direct array
                event_types_list = data.get(
                    "event_types", data if isinstance(data, list) else []
                )

            event_types = []
            for et in event_types_list:
                event_type = self._parse_event_type(et)
                event_types.append(event_type)

            return EventTypeListResponse(
                event_types=event_types,
                total_count=len(event_types),
                message=f"Retrieved {len(event_types)} event types successfully",
            )

        except httpx.HTTPError as e:
            logger.error(f"Error fetching event types: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error fetching event types: {e}")
            raise

    async def get_me(self) -> Dict[str, Any]:
        """Get current user information from Cal.com /me endpoint"""
        try:
            params = self._get_params()
            response = await self.client.get("/me", params=params)
            response.raise_for_status()
            data = response.json()

            # Handle v2 API response format with status/data wrapper
            if data.get("status") == "success" and "data" in data:
                user_data = data["data"]
            else:
                # Fallback for v1 format - some responses wrap in "user", others don't
                user_data = data.get("user", data)

            return {
                "id": user_data.get("id"),
                "username": user_data.get("username"),
                "email": user_data.get("email"),
                "name": user_data.get("name"),
                "timeZone": user_data.get("timeZone"),
            }

        except httpx.HTTPError as e:
            logger.error(f"Error fetching current user info: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error fetching current user info: {e}")
            raise

    async def check_availability(
        self, availability_request: AvailabilityRequest
    ) -> AvailabilityResponse:
        """Check available time slots using Cal.com v2 API /slots endpoint.

        Uses v2 API with Bearer token authentication for availability checking.
        """
        logger.debug("Using v2 API /slots endpoint for availability checking")

        try:
            # v2 API uses Bearer token authentication (already configured in self.client)
            v2_params = {
                "eventTypeId": availability_request.event_type_id,
                "start": availability_request.date_from,
                "end": availability_request.date_to
                or availability_request.date_from,  # Use date_to if provided, otherwise same as date_from
                "timeZone": availability_request.timezone,
            }

            # Add cal-api-version header for v2 API
            headers = {"cal-api-version": "2024-09-04"}

            # Use the /slots endpoint
            endpoint = "/slots"
            logger.debug(f"Making request to v2 slots endpoint: {endpoint}")
            logger.debug(f"Request params: {v2_params}")

            response = await self.client.get(
                endpoint, params=v2_params, headers=headers
            )
            if response.status_code == 200:
                response_data = response.json()
                logger.debug(f"Raw v2 slots response: {response_data}")

                available_slots = []

                # Handle v2 API response format with status/data wrapper
                if response_data.get("status") == "success" and "data" in response_data:
                    data = response_data["data"]
                else:
                    # Fallback for direct response format
                    data = response_data

                # Parse v2 slots response format
                # The response is a map where each key is a date and value is array of slots
                if isinstance(data, dict):
                    for date_key, time_slots in data.items():
                        if isinstance(time_slots, list):
                            for slot in time_slots:
                                if isinstance(slot, dict):
                                    # Handle range format (when format=range is used)
                                    start_time = slot.get("start")
                                    end_time = slot.get("end")
                                    attendees_count = slot.get("attendeesCount", 0)

                                    if start_time and end_time:
                                        # Create availability slot with start-end range
                                        available_slots.append(
                                            AvailabilitySlot(
                                                time=f"{start_time} - {end_time}",
                                                attendees=attendees_count,
                                                booking_url=None,  # v2 slots doesn't provide booking URL directly
                                            )
                                        )
                                    elif start_time:
                                        # Handle time format (default format)
                                        available_slots.append(
                                            AvailabilitySlot(
                                                time=start_time,
                                                attendees=attendees_count,
                                                booking_url=None,
                                            )
                                        )
                                elif isinstance(slot, str):
                                    # Handle simple time string format (default format)
                                    available_slots.append(
                                        AvailabilitySlot(
                                            time=slot,
                                            attendees=0,
                                            booking_url=None,
                                        )
                                    )

                logger.info(
                    f"Successfully retrieved {len(available_slots)} availability slots from v2 /slots endpoint"
                )
                return AvailabilityResponse(
                    success=True,
                    event_type_id=availability_request.event_type_id,
                    date_from=availability_request.date_from,
                    date_to=availability_request.date_to,
                    available_slots=available_slots,
                    timezone=availability_request.timezone,
                    message=f"Found {len(available_slots)} available time slots for {availability_request.date_from}",
                    total_slots=len(available_slots),
                )

            else:
                error_message = f"v2 /slots endpoint returned status {response.status_code}: {response.text}"
                logger.error(error_message)
                return AvailabilityResponse(
                    success=False,
                    event_type_id=availability_request.event_type_id,
                    date_from=availability_request.date_from,
                    date_to=availability_request.date_to,
                    available_slots=[],
                    timezone=availability_request.timezone,
                    message=error_message,
                    total_slots=0,
                )

        except Exception as e:
            error_message = f"Error checking availability with v2 API: {str(e)}"
            logger.error(error_message)
            return AvailabilityResponse(
                success=False,
                event_type_id=availability_request.event_type_id,
                date_from=availability_request.date_from,
                date_to=availability_request.date_to,
                available_slots=[],
                timezone=availability_request.timezone,
                message=error_message,
                total_slots=0,
            )
