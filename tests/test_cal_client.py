import pytest
import httpx
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timezone, timedelta

from src.cal_client import CalcomClient
from src.models import (
    CalcomBooking,
    CreateBookingRequest,
    EventType,
    BookingListResponse,
    EventTypeListResponse,
    CreateBookingResponse,
    AvailabilityRequest,
    AvailabilityResponse,
    AvailabilitySlot,
)


class TestCalcomClient:
    """Test cases for CalcomClient class"""

    @pytest.fixture
    def mock_settings(self):
        """Mock settings for testing"""
        with patch("src.cal_client.settings") as mock_settings:
            mock_settings.calcom_base_url = "https://api.cal.com/v2"
            mock_settings.calcom_api_key = "test-api-key"
            yield mock_settings

    @pytest.fixture
    def client(self, mock_settings):
        """Create a CalcomClient instance for testing"""
        return CalcomClient(api_key="test-api-key")

    @pytest.mark.asyncio
    async def test_client_initialization(self, mock_settings):
        """Test client initialization"""
        client = CalcomClient(api_key="test-api-key")

        assert client.base_url == "https://api.cal.com/v2"
        assert client.api_key == "test-api-key"
        assert isinstance(client.client, httpx.AsyncClient)

    @pytest.mark.asyncio
    async def test_client_initialization_with_fallback(self, mock_settings):
        """Test client initialization with fallback to settings"""
        client = CalcomClient()  # Should fallback to settings

        assert client.base_url == "https://api.cal.com/v2"
        assert client.api_key == "test-api-key"
        assert isinstance(client.client, httpx.AsyncClient)

    @pytest.mark.asyncio
    async def test_client_initialization_no_api_key(self):
        """Test client initialization fails without API key"""
        with patch("src.cal_client.settings") as mock_settings:
            mock_settings.calcom_base_url = "https://api.cal.com/v2"
            mock_settings.calcom_api_key = ""  # Empty API key

            with pytest.raises(ValueError, match="Cal.com API key is required"):
                CalcomClient()

    @pytest.mark.asyncio
    async def test_context_manager(self, client):
        """Test async context manager functionality"""
        async with client as c:
            assert c is client
        # Client should be closed after context exit

    def test_get_params(self, client):
        """Test parameter building"""
        params = client._get_params()
        assert isinstance(params, dict)

        additional_params = {"test": "value"}
        params = client._get_params(additional_params)
        assert params["test"] == "value"

    def test_extract_location(self, client):
        """Test location extraction from various formats"""
        # Test string location
        assert client._extract_location("Zoom Meeting") == "Zoom Meeting"

        # Test empty string
        assert client._extract_location("") is None
        assert client._extract_location("   ") is None

        # Test dict location with value
        location_dict = {"value": "Conference Room A"}
        assert client._extract_location(location_dict) == "Conference Room A"

        # Test dict location with address
        location_dict = {"address": "123 Main St"}
        assert client._extract_location(location_dict) == "123 Main St"

        # Test None location
        assert client._extract_location(None) is None

    def test_parse_booking_success(self, client):
        """Test successful booking parsing"""
        booking_data = {
            "id": 123,
            "title": "Test Meeting",
            "description": "Test description",
            "startTime": "2024-01-15T14:00:00Z",
            "endTime": "2024-01-15T15:00:00Z",
            "attendees": [{"name": "John Doe", "email": "<EMAIL>"}],
            "location": "Zoom",
            "eventTypeId": 456,
            "status": "confirmed",
            "uid": "test-uid-123",
        }

        booking = client._parse_booking(booking_data)

        assert isinstance(booking, CalcomBooking)
        assert booking.id == 123
        assert booking.title == "Test Meeting"
        assert booking.event_type_id == 456
        assert booking.uid == "test-uid-123"

    def test_parse_booking_v2_format(self, client):
        """Test booking parsing with v2 API format"""
        booking_data = {
            "id": 123,
            "title": "Test Meeting",
            "start": "2024-01-15T14:00:00Z",  # v2 uses 'start' instead of 'startTime'
            "end": "2024-01-15T15:00:00Z",  # v2 uses 'end' instead of 'endTime'
            "eventType": {"id": 456},  # v2 nests event type
            "uid": "test-uid-123",
        }

        booking = client._parse_booking(booking_data)

        assert booking.event_type_id == 456
        assert booking.start_time.year == 2024

    def test_parse_booking_missing_required_fields(self, client):
        """Test booking parsing with missing required fields"""
        # Missing start time
        booking_data = {
            "id": 123,
            "endTime": "2024-01-15T15:00:00Z",
            "eventTypeId": 456,
        }

        with pytest.raises(ValueError, match="Missing start time"):
            client._parse_booking(booking_data)

        # Missing end time
        booking_data = {
            "id": 123,
            "startTime": "2024-01-15T14:00:00Z",
            "eventTypeId": 456,
        }

        with pytest.raises(ValueError, match="Missing end time"):
            client._parse_booking(booking_data)

        # Missing event type ID
        booking_data = {
            "id": 123,
            "startTime": "2024-01-15T14:00:00Z",
            "endTime": "2024-01-15T15:00:00Z",
        }

        with pytest.raises(ValueError, match="Missing event type ID"):
            client._parse_booking(booking_data)

    def test_parse_event_type(self, client):
        """Test event type parsing"""
        event_type_data = {
            "id": 123,
            "title": "30 Min Meeting",
            "slug": "30min",
            "length": 30,
            "description": "Quick meeting",
            "locations": [{"type": "zoom"}],
        }

        event_type = client._parse_event_type(event_type_data)

        assert isinstance(event_type, EventType)
        assert event_type.id == 123
        assert event_type.title == "30 Min Meeting"
        assert event_type.length == 30

    def test_parse_event_type_v2_format(self, client):
        """Test event type parsing with v2 format"""
        event_type_data = {
            "id": 123,
            "title": "60 Min Meeting",
            "slug": "60min",
            "lengthInMinutes": 60,  # v2 uses different field name
        }

        event_type = client._parse_event_type(event_type_data)

        assert event_type.length == 60


class TestGetBookings:
    """Test cases for get_bookings method"""

    @pytest.fixture
    def client(self):
        with patch("src.cal_client.settings") as mock_settings:
            mock_settings.calcom_base_url = "https://api.cal.com/v2"
            mock_settings.calcom_api_key = "test-api-key"
            yield CalcomClient(api_key="test-api-key")

    @pytest.mark.asyncio
    async def test_get_bookings_success_v2_format(self, client):
        """Test successful booking fetch with v2 API format"""
        mock_response_data = {
            "status": "success",
            "data": [
                {
                    "id": 123,
                    "title": "Test Meeting",
                    "start": "2024-01-15T14:00:00Z",
                    "end": "2024-01-15T15:00:00Z",
                    "eventTypeId": 456,
                    "uid": "test-uid-123",
                }
            ],
        }

        with patch.object(client.client, "get") as mock_get:
            mock_response = MagicMock()
            mock_response.json.return_value = mock_response_data
            mock_response.raise_for_status.return_value = None
            mock_get.return_value = mock_response

            result = await client.get_bookings(limit=10)

            assert isinstance(result, BookingListResponse)
            assert len(result.bookings) == 1
            assert result.total_count == 1
            assert result.bookings[0].id == 123

    @pytest.mark.asyncio
    async def test_get_bookings_with_filters(self, client):
        """Test booking fetch with various filters"""
        mock_response_data = {"status": "success", "data": []}

        with patch.object(client.client, "get") as mock_get:
            mock_response = MagicMock()
            mock_response.json.return_value = mock_response_data
            mock_response.raise_for_status.return_value = None
            mock_get.return_value = mock_response

            start_date = datetime.now(timezone.utc)
            end_date = start_date + timedelta(days=7)

            await client.get_bookings(
                limit=50,
                status="confirmed",
                start_date=start_date,
                end_date=end_date,
                uid="test-uid",
            )

            # Verify correct parameters were passed
            call_args = mock_get.call_args
            params = call_args[1]["params"]
            assert params["take"] == 50
            assert params["status"] == "confirmed"
            assert params["uid"] == "test-uid"
            assert "startTime" in params
            assert "endTime" in params

    @pytest.mark.asyncio
    async def test_get_bookings_invalid_data(self, client):
        """Test booking fetch with invalid booking data"""
        mock_response_data = {
            "status": "success",
            "data": [
                {
                    "id": 123,
                    # Missing required fields
                }
            ],
        }

        with patch.object(client.client, "get") as mock_get:
            mock_response = MagicMock()
            mock_response.json.return_value = mock_response_data
            mock_response.raise_for_status.return_value = None
            mock_get.return_value = mock_response

            result = await client.get_bookings()

            # Should skip invalid bookings and return empty list
            assert len(result.bookings) == 0

    @pytest.mark.asyncio
    async def test_get_bookings_http_error(self, client):
        """Test booking fetch with HTTP error"""
        with patch.object(client.client, "get") as mock_get:
            mock_get.side_effect = httpx.HTTPError("Network error")

            with pytest.raises(httpx.HTTPError):
                await client.get_bookings()


class TestCreateBooking:
    """Test cases for create_booking method"""

    @pytest.fixture
    def client(self):
        with patch("src.cal_client.settings") as mock_settings:
            mock_settings.calcom_base_url = "https://api.cal.com/v2"
            mock_settings.calcom_api_key = "test-api-key"
            yield CalcomClient(api_key="test-api-key")

    @pytest.fixture
    def booking_request(self):
        return CreateBookingRequest(
            event_type_id=456,
            start_time="2024-01-15T14:00:00",
            attendee_name="John Doe",
            attendee_email="<EMAIL>",
            timezone="UTC",
        )

    @pytest.mark.asyncio
    async def test_create_booking_success(self, client, booking_request):
        """Test successful booking creation"""
        # Mock get_event_types response
        mock_event_types = EventTypeListResponse(
            event_types=[
                EventType(id=456, title="Test Event", slug="test-event", length=30)
            ],
            total_count=1,
            message="Success",
        )

        # Mock get_me response
        mock_user_info = {"id": 123, "username": "testuser"}

        # Mock create booking response
        mock_booking_response = {
            "status": "success",
            "data": {
                "id": 789,
                "uid": "booking-uid-123",
                "metadata": {"videoCallUrl": "https://zoom.us/j/123456789"},
            },
        }

        with patch.object(
            client, "get_event_types", return_value=mock_event_types
        ), patch.object(client, "get_me", return_value=mock_user_info), patch.object(
            client.client, "post"
        ) as mock_post:

            mock_response = MagicMock()
            mock_response.json.return_value = mock_booking_response
            mock_post.return_value = mock_response

            result = await client.create_booking(booking_request)

            assert isinstance(result, CreateBookingResponse)
            assert result.success is True
            assert result.booking_id == 789
            assert result.booking_uid == "booking-uid-123"

    @pytest.mark.asyncio
    async def test_create_booking_event_type_not_found(self, client, booking_request):
        """Test booking creation with invalid event type"""
        mock_event_types = EventTypeListResponse(
            event_types=[],  # No event types
            total_count=0,
            message="No event types found",
        )

        with patch.object(client, "get_event_types", return_value=mock_event_types):
            result = await client.create_booking(booking_request)

            assert result.success is False
            assert result.error_code == "event_type_not_found"

    @pytest.mark.asyncio
    async def test_create_booking_timezone_conversion(self, client):
        """Test that timezone conversion works correctly"""
        # Create a booking request with EST timezone
        booking_request = CreateBookingRequest(
            event_type_id=456,
            start_time="2024-01-15T14:00:00",  # 2 PM EST
            attendee_name="John Doe",
            attendee_email="<EMAIL>",
            timezone="America/New_York",  # EST timezone
        )

        # Mock get_event_types response with Google Meet location
        mock_event_types = EventTypeListResponse(
            event_types=[
                EventType(
                    id=456,
                    title="Test Event",
                    slug="test-event",
                    length=30,
                    locations=[{"type": "integrations:google:meet"}],
                )
            ],
            total_count=1,
            message="Event types retrieved successfully",
        )

        # Mock get_me response
        mock_me_response = {
            "status": "success",
            "data": {"username": "testuser"},
        }

        # Mock successful booking creation response
        mock_booking_response = {
            "status": "success",
            "data": {
                "id": 789,
                "uid": "booking-uid-123",
                "title": "Test Event",
                "startTime": "2024-01-15T19:00:00.000Z",  # Expected UTC time (EST + 5 hours)
                "endTime": "2024-01-15T19:30:00.000Z",
            },
        }

        with patch.object(
            client, "get_event_types", return_value=mock_event_types
        ), patch.object(client, "get_me", return_value=mock_me_response), patch.object(
            client.client, "post"
        ) as mock_post:

            mock_response = MagicMock()
            mock_response.json.return_value = mock_booking_response
            mock_response.raise_for_status.return_value = None
            mock_post.return_value = mock_response

            result = await client.create_booking(booking_request)

            # Verify the booking was created successfully
            assert result.success is True

            # Verify that the API was called with UTC time
            call_args = mock_post.call_args
            booking_data = call_args[1]["json"]

            # Check that v2 API format is used
            assert "attendee" in booking_data
            assert "location" in booking_data
            assert booking_data["location"]["type"] == "integrations:google:meet"

            # The start time should be converted to UTC (EST is UTC-5, so 14:00 EST = 19:00 UTC)
            assert booking_data["start"] == "2024-01-15T19:00:00Z"

            # Check attendee information
            assert booking_data["attendee"]["name"] == "John Doe"
            assert booking_data["attendee"]["email"] == "<EMAIL>"
            assert booking_data["attendee"]["timeZone"] == "America/New_York"

    @pytest.mark.asyncio
    async def test_create_booking_utc_timezone_passthrough(self, client):
        """Test that UTC timezone times are passed through correctly"""
        # Create a booking request with UTC timezone
        booking_request = CreateBookingRequest(
            event_type_id=456,
            start_time="2024-01-15T14:00:00Z",  # Already in UTC format
            attendee_name="John Doe",
            attendee_email="<EMAIL>",
            timezone="UTC",
        )

        # Mock get_event_types response with Google Meet location
        mock_event_types = EventTypeListResponse(
            event_types=[
                EventType(
                    id=456,
                    title="Test Event",
                    slug="test-event",
                    length=60,
                    locations=[{"type": "integrations:google:meet"}],
                )
            ],
            total_count=1,
            message="Event types retrieved successfully",
        )

        # Mock get_me response
        mock_me_response = {
            "status": "success",
            "data": {"username": "testuser"},
        }

        # Mock successful booking creation response
        mock_booking_response = {
            "status": "success",
            "data": {
                "id": 789,
                "uid": "booking-uid-123",
                "title": "Test Event",
                "startTime": "2024-01-15T14:00:00.000Z",
                "endTime": "2024-01-15T15:00:00.000Z",
            },
        }

        with patch.object(
            client, "get_event_types", return_value=mock_event_types
        ), patch.object(client, "get_me", return_value=mock_me_response), patch.object(
            client.client, "post"
        ) as mock_post:

            mock_response = MagicMock()
            mock_response.json.return_value = mock_booking_response
            mock_response.raise_for_status.return_value = None
            mock_post.return_value = mock_response

            result = await client.create_booking(booking_request)

            # Verify the booking was created successfully
            assert result.success is True

            # Verify that the API was called with the same UTC time
            call_args = mock_post.call_args
            booking_data = call_args[1]["json"]

            # Check that v2 API format is used
            assert "attendee" in booking_data
            assert "location" in booking_data
            assert booking_data["location"]["type"] == "integrations:google:meet"

            # The start time should remain the same since it's already UTC
            assert booking_data["start"] == "2024-01-15T14:00:00Z"

            # Check attendee information
            assert booking_data["attendee"]["name"] == "John Doe"
            assert booking_data["attendee"]["email"] == "<EMAIL>"
            assert booking_data["attendee"]["timeZone"] == "UTC"

    @pytest.mark.asyncio
    async def test_create_booking_fallback_to_cal_video(self, client):
        """Test that booking falls back to Cal Video when Google Meet is not available"""
        # Create a booking request with UTC timezone
        booking_request = CreateBookingRequest(
            event_type_id=456,
            start_time="2024-01-15T14:00:00",
            attendee_name="John Doe",
            attendee_email="<EMAIL>",
            timezone="UTC",
        )

        # Mock get_event_types response WITHOUT Google Meet location
        mock_event_types = EventTypeListResponse(
            event_types=[
                EventType(
                    id=456,
                    title="Test Event",
                    slug="test-event",
                    length=60,
                    locations=[{"type": "address"}],  # No Google Meet
                )
            ],
            total_count=1,
            message="Event types retrieved successfully",
        )

        # Mock get_me response
        mock_me_response = {
            "status": "success",
            "data": {"username": "testuser"},
        }

        # Mock successful booking creation response
        mock_booking_response = {
            "status": "success",
            "data": {
                "id": 789,
                "uid": "booking-uid-123",
                "title": "Test Event",
                "startTime": "2024-01-15T14:00:00.000Z",
                "endTime": "2024-01-15T15:00:00.000Z",
            },
        }

        with patch.object(
            client, "get_event_types", return_value=mock_event_types
        ), patch.object(client, "get_me", return_value=mock_me_response), patch.object(
            client.client, "post"
        ) as mock_post:

            mock_response = MagicMock()
            mock_response.json.return_value = mock_booking_response
            mock_response.raise_for_status.return_value = None
            mock_post.return_value = mock_response

            result = await client.create_booking(booking_request)

            # Verify the booking was created successfully
            assert result.success is True

            # Verify that the API was called with Cal Video fallback
            call_args = mock_post.call_args
            booking_data = call_args[1]["json"]

            # Should fallback to Cal Video
            assert booking_data["location"]["type"] == "integrations:cal:video"

    @pytest.mark.asyncio
    async def test_create_booking_http_error(self, client, booking_request):
        """Test booking creation with HTTP error"""
        mock_event_types = EventTypeListResponse(
            event_types=[
                EventType(id=456, title="Test Event", slug="test-event", length=30)
            ],
            total_count=1,
            message="Success",
        )

        mock_user_info = {"id": 123, "username": "testuser"}

        error_response = MagicMock()
        error_response.status_code = 400
        error_response.json.return_value = {
            "message": "no_available_users_found_error",
            "error": "no_available_users_found_error",
        }

        with patch.object(
            client, "get_event_types", return_value=mock_event_types
        ), patch.object(client, "get_me", return_value=mock_user_info), patch.object(
            client.client, "post"
        ) as mock_post:

            mock_post.side_effect = httpx.HTTPStatusError(
                "Bad Request", request=MagicMock(), response=error_response
            )

            result = await client.create_booking(booking_request)

            assert result.success is False
            assert result.error_code == "no_available_users_found_error"


class TestGetEventTypes:
    """Test cases for get_event_types method"""

    @pytest.fixture
    def client(self):
        with patch("src.cal_client.settings") as mock_settings:
            mock_settings.calcom_base_url = "https://api.cal.com/v2"
            mock_settings.calcom_api_key = "test-api-key"
            yield CalcomClient(api_key="test-api-key")

    @pytest.mark.asyncio
    async def test_get_event_types_v2_format(self, client):
        """Test event types fetch with v2 API format"""
        mock_response_data = {
            "status": "success",
            "data": {
                "eventTypeGroups": [
                    {
                        "eventTypes": [
                            {
                                "id": 123,
                                "title": "30 Min Meeting",
                                "slug": "30min",
                                "length": 30,
                            }
                        ]
                    }
                ]
            },
        }

        with patch.object(client.client, "get") as mock_get:
            mock_response = MagicMock()
            mock_response.json.return_value = mock_response_data
            mock_response.raise_for_status.return_value = None
            mock_get.return_value = mock_response

            result = await client.get_event_types()

            assert isinstance(result, EventTypeListResponse)
            assert len(result.event_types) == 1
            assert result.event_types[0].id == 123

    @pytest.mark.asyncio
    async def test_get_event_types_direct_array(self, client):
        """Test event types fetch with direct array format"""
        mock_response_data = {
            "status": "success",
            "data": [
                {
                    "id": 123,
                    "title": "30 Min Meeting",
                    "slug": "30min",
                    "length": 30,
                }
            ],
        }

        with patch.object(client.client, "get") as mock_get:
            mock_response = MagicMock()
            mock_response.json.return_value = mock_response_data
            mock_response.raise_for_status.return_value = None
            mock_get.return_value = mock_response

            result = await client.get_event_types()

            assert len(result.event_types) == 1


class TestGetMe:
    """Test cases for get_me method"""

    @pytest.fixture
    def client(self):
        with patch("src.cal_client.settings") as mock_settings:
            mock_settings.calcom_base_url = "https://api.cal.com/v2"
            mock_settings.calcom_api_key = "test-api-key"
            yield CalcomClient(api_key="test-api-key")

    @pytest.mark.asyncio
    async def test_get_me_v2_format(self, client):
        """Test get_me with v2 API format"""
        mock_response_data = {
            "status": "success",
            "data": {
                "id": 123,
                "username": "testuser",
                "email": "<EMAIL>",
                "name": "Test User",
                "timeZone": "UTC",
            },
        }

        with patch.object(client.client, "get") as mock_get:
            mock_response = MagicMock()
            mock_response.json.return_value = mock_response_data
            mock_response.raise_for_status.return_value = None
            mock_get.return_value = mock_response

            result = await client.get_me()

            assert result["id"] == 123
            assert result["username"] == "testuser"
            assert result["email"] == "<EMAIL>"

    @pytest.mark.asyncio
    async def test_get_me_v1_format(self, client):
        """Test get_me with v1 API format"""
        mock_response_data = {
            "user": {
                "id": 123,
                "username": "testuser",
                "email": "<EMAIL>",
            }
        }

        with patch.object(client.client, "get") as mock_get:
            mock_response = MagicMock()
            mock_response.json.return_value = mock_response_data
            mock_response.raise_for_status.return_value = None
            mock_get.return_value = mock_response

            result = await client.get_me()

            assert result["id"] == 123
            assert result["username"] == "testuser"


class TestCheckAvailability:
    """Test cases for check_availability method"""

    @pytest.fixture
    def client(self):
        with patch("src.cal_client.settings") as mock_settings:
            mock_settings.calcom_base_url = "https://api.cal.com/v2"
            mock_settings.calcom_api_key = "test-api-key"
            yield CalcomClient(api_key="test-api-key")

    @pytest.fixture
    def availability_request(self):
        return AvailabilityRequest(
            event_type_id=456,
            date_from="2024-01-15",
            date_to="2024-01-15",
            timezone="UTC",
        )

    @pytest.mark.asyncio
    async def test_check_availability_success(self, client, availability_request):
        """Test successful availability check with v2 slots endpoint"""
        mock_response_data = {
            "data": {
                "2024-01-15": [
                    {"start": "2024-01-15T09:00:00.000Z"},
                    {"start": "2024-01-15T10:00:00.000Z"},
                    {"start": "2024-01-15T11:00:00.000Z"},
                ]
            },
            "status": "success",
        }

        with patch.object(client.client, "get") as mock_get:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = mock_response_data
            mock_get.return_value = mock_response

            result = await client.check_availability(availability_request)

            assert isinstance(result, AvailabilityResponse)
            assert result.success is True
            assert len(result.available_slots) == 3
            assert result.total_slots == 3
            assert result.available_slots[0].time == "2024-01-15T09:00:00.000Z"

            # Verify the correct endpoint and headers were used
            mock_get.assert_called_once()
            call_args = mock_get.call_args
            assert call_args[0][0] == "/slots"  # endpoint
            assert call_args[1]["headers"]["cal-api-version"] == "2024-09-04"
            assert call_args[1]["params"]["eventTypeId"] == 456
            assert call_args[1]["params"]["start"] == "2024-01-15"
            assert call_args[1]["params"]["end"] == "2024-01-15"
            assert call_args[1]["params"]["timeZone"] == "UTC"

    @pytest.mark.asyncio
    async def test_check_availability_v2_api_error(self, client, availability_request):
        """Test availability check with v2 API error"""
        with patch.object(client.client, "get") as mock_get:
            mock_response = MagicMock()
            mock_response.status_code = 400
            mock_response.text = "Bad Request"
            mock_get.return_value = mock_response

            result = await client.check_availability(availability_request)

            assert result.success is False
            assert "400" in result.message

    @pytest.mark.asyncio
    async def test_check_availability_range_format(self, client, availability_request):
        """Test availability check with range format response"""
        mock_response_data = {
            "data": {
                "2024-01-15": [
                    {
                        "start": "2024-01-15T09:00:00.000Z",
                        "end": "2024-01-15T09:30:00.000Z",
                        "attendeesCount": 0,
                    },
                    {
                        "start": "2024-01-15T10:00:00.000Z",
                        "end": "2024-01-15T10:30:00.000Z",
                        "attendeesCount": 1,
                    },
                ]
            },
            "status": "success",
        }

        with patch.object(client.client, "get") as mock_get:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = mock_response_data
            mock_get.return_value = mock_response

            result = await client.check_availability(availability_request)

            assert result.success is True
            assert len(result.available_slots) == 2
            assert (
                result.available_slots[0].time
                == "2024-01-15T09:00:00.000Z - 2024-01-15T09:30:00.000Z"
            )
            assert result.available_slots[0].attendees == 0
            assert result.available_slots[1].attendees == 1

    @pytest.mark.asyncio
    async def test_check_availability_exception(self, client, availability_request):
        """Test availability check with exception"""
        with patch.object(client.client, "get", side_effect=Exception("Network error")):
            result = await client.check_availability(availability_request)

            assert result.success is False
            assert "Error checking availability with v2 API" in result.message


class TestGetBookingByUid:
    """Test cases for get_booking_by_uid method"""

    @pytest.fixture
    def client(self):
        with patch("src.cal_client.settings") as mock_settings:
            mock_settings.calcom_base_url = "https://api.cal.com/v2"
            mock_settings.calcom_api_key = "test-api-key"
            yield CalcomClient(api_key="test-api-key")

    @pytest.mark.asyncio
    async def test_get_booking_by_uid_found(self, client):
        """Test getting booking by UID when found"""
        mock_booking = CalcomBooking(
            id=123,
            title="Test Meeting",
            start_time=datetime.now(timezone.utc),
            end_time=datetime.now(timezone.utc) + timedelta(hours=1),
            attendees=[],
            event_type_id=456,
            uid="test-uid-123",
        )

        mock_response = BookingListResponse(
            bookings=[mock_booking], total_count=1, message="Found"
        )

        with patch.object(client, "get_bookings", return_value=mock_response):
            result = await client.get_booking_by_uid("test-uid-123")

            assert result is not None
            assert result.uid == "test-uid-123"

    @pytest.mark.asyncio
    async def test_get_booking_by_uid_not_found(self, client):
        """Test getting booking by UID when not found"""
        mock_response = BookingListResponse(
            bookings=[], total_count=0, message="Not found"
        )

        with patch.object(client, "get_bookings", return_value=mock_response):
            result = await client.get_booking_by_uid("nonexistent-uid")

            assert result is None

    @pytest.mark.asyncio
    async def test_get_booking_by_uid_error(self, client):
        """Test getting booking by UID with error"""
        with patch.object(client, "get_bookings", side_effect=Exception("API Error")):
            with pytest.raises(Exception):
                await client.get_booking_by_uid("test-uid-123")


if __name__ == "__main__":
    pytest.main([__file__])
