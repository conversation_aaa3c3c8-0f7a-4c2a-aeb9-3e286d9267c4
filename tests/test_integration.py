import pytest
import asyncio
from unittest.mock import Async<PERSON>ock, <PERSON>Mock, patch, Mock
from datetime import datetime, timezone, timedelta
import sys

# Mock fastmcp before importing server module
sys.modules["fastmcp"] = Mock()

from src.cal_client import CalcomClient
from src.models import (
    CalcomBooking,
    CreateBookingRequest,
    EventType,
    BookingListResponse,
    EventTypeListResponse,
    CreateBookingResponse,
    AvailabilityRequest,
    AvailabilityResponse,
    AvailabilitySlot,
)


# Mock the FastMCP and Context classes
class MockContext:
    async def info(self, message):
        pass

    async def error(self, message):
        pass

    async def warning(self, message):
        pass


class MockTool:
    def __init__(self, name, description, inputSchema):
        self.name = name
        self.description = description
        self.inputSchema = inputSchema


class MockResource:
    def __init__(self, uri):
        self.uri = uri


class MockFastMCP:
    def __init__(self, name):
        self.name = name
        self._tools = []
        self._resources = []

    def tool(self, func):
        # Create a mock tool for testing
        tool = MockTool(
            name=func.__name__,
            description=func.__doc__ or "",
            inputSchema={"type": "object", "properties": {}, "required": []},
        )
        self._tools.append(tool)
        return func

    def resource(self, uri):
        def decorator(func):
            resource = MockResource(uri)
            self._resources.append(resource)
            return func

        return decorator

    def custom_route(self, path, methods=None):
        def decorator(func):
            return func

        return decorator

    async def _list_tools(self):
        return self._tools

    async def _list_resources(self):
        return self._resources


# Patch fastmcp imports
with patch.dict(
    "sys.modules", {"fastmcp": Mock(FastMCP=MockFastMCP, Context=MockContext)}
):
    from src.server import (
        mcp,
        list_bookings,
        create_booking,
        list_event_types,
        check_availability,
        get_server_info,
        get_booking_details,
        health_check,
    )


class TestMCPServerIntegration:
    """Integration tests for the MCP server functionality"""

    @pytest.mark.asyncio
    async def test_mcp_server_initialization(self):
        """Test that MCP server initializes correctly"""
        assert mcp is not None
        assert mcp.name is not None

    @pytest.mark.asyncio
    async def test_tool_registration(self):
        """Test that all tools are properly registered with MCP"""
        # Get registered tools using the correct FastMCP API
        tools = await mcp._list_tools()

        expected_tools = [
            "list_bookings",
            "create_booking",
            "list_event_types",
            "check_availability",
        ]

        tool_names = [tool.name for tool in tools]

        for expected_tool in expected_tools:
            assert expected_tool in tool_names

    @pytest.mark.asyncio
    async def test_resource_registration(self):
        """Test that resources are properly registered with MCP"""
        resources = await mcp._list_resources()

        # Check for server info resource
        resource_uris = [resource.uri for resource in resources]
        assert "resource://server/info" in resource_uris


class TestBasicValidation:
    """Test basic validation functionality that doesn't require complex mocking"""

    @pytest.mark.asyncio
    async def test_booking_creation_with_validation_errors(self):
        """Test booking creation with various validation scenarios"""
        # Test with invalid email
        # Create a mock context for the test
        mock_ctx = MockContext()

        result = await create_booking(
            event_type_id=456,
            start_time="2024-01-15T09:00:00",
            attendee_name="John Doe",
            attendee_email="invalid-email",  # Invalid email format
            ctx=mock_ctx,
        )

        # Should handle validation error - the function returns a string with error message
        assert isinstance(result, str)
        assert "❌" in result or "Error" in result

    @pytest.mark.asyncio
    async def test_availability_check_edge_cases(self):
        """Test availability checking with edge cases"""
        # Test with invalid date format
        # Create a mock context for the test
        mock_ctx = MockContext()

        result = await check_availability(
            event_type_id=456,
            date="invalid-date",
            ctx=mock_ctx,
        )

        # Should handle validation error - the function returns a string with error message
        assert isinstance(result, str)
        assert "❌" in result or "Error" in result


class TestConcurrentOperations:
    """Test concurrent operations and race conditions"""

    @pytest.mark.asyncio
    async def test_concurrent_client_connections(self):
        """Test concurrent CalcomClient connections"""

        async def create_and_use_client():
            async with CalcomClient() as client:
                # Simulate some work
                await asyncio.sleep(0.1)
                return "success"

        # Run multiple concurrent client operations
        tasks = [create_and_use_client() for _ in range(5)]
        results = await asyncio.gather(*tasks)

        # All should complete successfully
        assert all(result == "success" for result in results)


class TestResourceAccess:
    """Test MCP resource access functionality"""

    @pytest.mark.asyncio
    async def test_server_info_resource(self):
        """Test server info resource access"""
        result = get_server_info()

        assert result["name"] == "Cal.com MCP Server"
        assert result["version"] == "2.0.0"
        assert "capabilities" in result
        assert isinstance(result["capabilities"], list)
        assert len(result["capabilities"]) > 0

    @pytest.mark.asyncio
    async def test_server_capabilities_reporting(self):
        """Test server capabilities reporting"""
        result = get_server_info()

        expected_capabilities = [
            "list_bookings",
            "create_booking",
            "list_event_types",
            "check_availability",
        ]

        for capability in expected_capabilities:
            assert capability in result["capabilities"]


class TestHealthAndMonitoring:
    """Test health check and monitoring functionality"""

    @pytest.mark.asyncio
    async def test_health_check_endpoint(self):
        """Test health check endpoint"""
        mock_request = MagicMock()

        response = await health_check(mock_request)

        assert response.status_code == 200
        body = response.body.decode()
        assert "healthy" in body
        assert "Cal.com MCP Server" in body
        assert "timestamp" in body


if __name__ == "__main__":
    pytest.main([__file__])
