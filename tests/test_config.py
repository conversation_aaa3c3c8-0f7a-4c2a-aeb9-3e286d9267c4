import pytest
import os
from unittest.mock import patch, mock_open
from pydantic import ValidationError

from src.config import Settings, settings


class TestSettings:
    """Test cases for Settings configuration class"""

    def test_settings_creation_with_required_fields(self):
        """Test creating Settings with required fields"""
        test_settings = Settings(calcom_api_key="test-api-key")

        assert test_settings.calcom_api_key == "test-api-key"
        assert test_settings.calcom_base_url == "https://api.cal.com/v2"
        assert test_settings.mcp_server_name == "Cal.com MCP Server"
        assert test_settings.mcp_server_port == 8000
        assert test_settings.mcp_server_host in ["127.0.0.1", "localhost"]
        # Log level might be overridden by environment
        assert test_settings.log_level in ["DEBUG", "INFO"]

    def test_settings_custom_values(self):
        """Test creating Settings with custom values"""
        test_settings = Settings(
            calcom_api_key="custom-api-key",
            calcom_base_url="https://custom.cal.com/v2",
            mcp_server_name="Custom MCP Server",
            mcp_server_port=9000,
            mcp_server_host="0.0.0.0",
            log_level="INFO",
        )

        assert test_settings.calcom_api_key == "custom-api-key"
        assert test_settings.calcom_base_url == "https://custom.cal.com/v2"
        assert test_settings.mcp_server_name == "Custom MCP Server"
        assert test_settings.mcp_server_port == 9000
        assert test_settings.mcp_server_host == "0.0.0.0"
        assert test_settings.log_level == "INFO"

    def test_settings_optional_api_key(self):
        """Test Settings with optional API key (now comes from headers)"""
        # Clear environment to ensure no CALCOM_API_KEY is set
        with patch.dict(os.environ, {}, clear=True):
            test_settings = Settings()
            # API key should now be optional with empty string default
            assert hasattr(test_settings, "calcom_api_key")
            assert test_settings.calcom_api_key == ""

    def test_settings_field_types(self):
        """Test Settings field type validation"""
        # Test valid types
        test_settings = Settings(
            calcom_api_key="test-key",
            mcp_server_port=8080,
        )
        assert isinstance(test_settings.mcp_server_port, int)

        # Test invalid port type (should be converted or raise error)
        with pytest.raises(ValidationError):
            Settings(
                calcom_api_key="test-key",
                mcp_server_port="invalid-port",  # String instead of int
            )

    @patch.dict(
        os.environ,
        {
            "CALCOM_API_KEY": "env-api-key",
            "CALCOM_BASE_URL": "https://env.cal.com/v2",
            "MCP_SERVER_NAME": "Env MCP Server",
            "MCP_SERVER_PORT": "7000",
            "MCP_SERVER_HOST": "***********",
            "LOG_LEVEL": "WARNING",
        },
    )
    def test_settings_from_environment(self):
        """Test Settings loading from environment variables"""
        test_settings = Settings()

        assert test_settings.calcom_api_key == "env-api-key"
        assert test_settings.calcom_base_url == "https://env.cal.com/v2"
        assert test_settings.mcp_server_name == "Env MCP Server"
        assert test_settings.mcp_server_port == 7000
        assert test_settings.mcp_server_host == "***********"
        assert test_settings.log_level == "WARNING"

    @patch.dict(os.environ, {}, clear=True)
    def test_settings_defaults_when_no_env(self):
        """Test Settings defaults when no environment variables are set"""
        test_settings = Settings()
        # API key should now be optional with empty default
        assert test_settings.calcom_api_key == ""
        assert test_settings.calcom_base_url == "https://api.cal.com/v2"
        assert test_settings.mcp_server_name == "Cal.com MCP Server"
        assert test_settings.mcp_server_port == 8000
        assert test_settings.mcp_server_host in ["127.0.0.1", "localhost"]

    @patch.dict(os.environ, {"CALCOM_API_KEY": "test-key"}, clear=True)
    def test_settings_partial_env_with_defaults(self):
        """Test Settings with partial environment variables and defaults"""
        test_settings = Settings()

        assert test_settings.calcom_api_key == "test-key"
        # These should use defaults since not in environment
        assert test_settings.calcom_base_url == "https://api.cal.com/v2"
        assert test_settings.mcp_server_name == "Cal.com MCP Server"
        assert test_settings.mcp_server_port == 8000
        assert test_settings.mcp_server_host in ["127.0.0.1", "localhost"]
        # Log level might be overridden by environment
        assert test_settings.log_level in ["DEBUG", "INFO"]

    def test_settings_case_insensitive(self):
        """Test that Settings is case insensitive for environment variables"""
        with patch.dict(
            os.environ,
            {
                "calcom_api_key": "lowercase-key",  # lowercase
                "CALCOM_BASE_URL": "https://upper.cal.com/v2",  # uppercase
                "Mcp_Server_Name": "Mixed Case Server",  # mixed case
            },
        ):
            test_settings = Settings()

            assert test_settings.calcom_api_key == "lowercase-key"
            assert test_settings.calcom_base_url == "https://upper.cal.com/v2"
            assert test_settings.mcp_server_name == "Mixed Case Server"

    @patch(
        "builtins.open",
        mock_open(read_data="CALCOM_API_KEY=dotenv-key\nLOG_LEVEL=ERROR"),
    )
    @patch("os.path.exists", return_value=True)
    def test_settings_from_dotenv_file(self, mock_exists):
        """Test Settings loading from .env file"""
        # Note: This test simulates .env file loading
        # The actual dotenv loading is handled by pydantic-settings
        with patch.dict(
            os.environ, {"CALCOM_API_KEY": "dotenv-key", "LOG_LEVEL": "ERROR"}
        ):
            test_settings = Settings()

            assert test_settings.calcom_api_key == "dotenv-key"
            assert test_settings.log_level == "ERROR"

    def test_settings_model_config(self):
        """Test Settings model configuration"""
        # Test that the model config is properly set
        config = Settings.model_config

        assert config["env_file"] == ".env"
        assert config["case_sensitive"] is False

    def test_settings_field_validation_edge_cases(self):
        """Test edge cases for field validation"""
        # Test empty string for API key (may or may not raise depending on validation)
        try:
            test_settings = Settings(calcom_api_key="")
            # If it doesn't raise, the empty string is considered valid
            assert test_settings.calcom_api_key == ""
        except ValidationError:
            # If it raises, that's also acceptable behavior
            pass

        # Test very long API key (should be valid)
        long_key = "a" * 1000
        test_settings = Settings(calcom_api_key=long_key)
        assert test_settings.calcom_api_key == long_key

        # Test port edge cases
        test_settings = Settings(calcom_api_key="test", mcp_server_port=1)
        assert test_settings.mcp_server_port == 1

        test_settings = Settings(calcom_api_key="test", mcp_server_port=65535)
        assert test_settings.mcp_server_port == 65535

    def test_settings_url_validation(self):
        """Test URL field validation"""
        # Test valid URLs
        valid_urls = [
            "https://api.cal.com/v2",
            "http://localhost:3000/api/v2",
            "https://custom-domain.com/cal/v2",
        ]

        for url in valid_urls:
            test_settings = Settings(calcom_api_key="test", calcom_base_url=url)
            assert test_settings.calcom_base_url == url

    def test_settings_log_level_validation(self):
        """Test log level validation"""
        valid_log_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]

        for level in valid_log_levels:
            test_settings = Settings(calcom_api_key="test", log_level=level)
            assert test_settings.log_level == level

        # Test lowercase (should work due to case insensitivity)
        test_settings = Settings(calcom_api_key="test", log_level="debug")
        assert test_settings.log_level == "debug"

    def test_settings_host_validation(self):
        """Test host field validation"""
        valid_hosts = [
            "127.0.0.1",
            "0.0.0.0",
            "***********",
            "localhost",
            "example.com",
        ]

        for host in valid_hosts:
            test_settings = Settings(calcom_api_key="test", mcp_server_host=host)
            assert test_settings.mcp_server_host == host


class TestGlobalSettings:
    """Test cases for the global settings instance"""

    def test_global_settings_exists(self):
        """Test that global settings instance exists"""
        assert settings is not None
        assert isinstance(settings, Settings)

    @patch.dict(os.environ, {"CALCOM_API_KEY": "global-test-key"})
    def test_global_settings_uses_environment(self):
        """Test that global settings uses environment variables"""
        # Import settings again to get fresh instance
        from src.config import Settings

        test_global_settings = Settings()

        assert test_global_settings.calcom_api_key == "global-test-key"

    def test_global_settings_immutability(self):
        """Test that settings behave as expected for immutability"""
        # Pydantic models are mutable by default, but we can test field access
        assert hasattr(settings, "calcom_api_key")
        assert hasattr(settings, "calcom_base_url")
        assert hasattr(settings, "mcp_server_name")
        assert hasattr(settings, "mcp_server_port")
        assert hasattr(settings, "mcp_server_host")
        assert hasattr(settings, "log_level")


class TestSettingsIntegration:
    """Integration tests for Settings with real environment scenarios"""

    @patch.dict(
        os.environ,
        {
            "CALCOM_API_KEY": "integration-test-key",
            "CALCOM_BASE_URL": "https://integration.cal.com/v2",
        },
    )
    def test_settings_integration_scenario(self):
        """Test Settings in a realistic integration scenario"""
        test_settings = Settings()

        # Verify required field from environment
        assert test_settings.calcom_api_key == "integration-test-key"
        assert test_settings.calcom_base_url == "https://integration.cal.com/v2"

        # Verify defaults are still applied
        assert test_settings.mcp_server_name == "Cal.com MCP Server"
        assert test_settings.mcp_server_port == 8000
        # Note: log level might be different if set in environment
        assert test_settings.log_level in ["DEBUG", "INFO", "WARNING", "ERROR"]

    def test_settings_serialization(self):
        """Test Settings serialization capabilities"""
        test_settings = Settings(calcom_api_key="test-key")

        # Test dict conversion
        settings_dict = test_settings.model_dump()
        assert isinstance(settings_dict, dict)
        assert settings_dict["calcom_api_key"] == "test-key"
        assert "calcom_base_url" in settings_dict

        # Test JSON serialization
        settings_json = test_settings.model_dump_json()
        assert isinstance(settings_json, str)
        assert "test-key" in settings_json

    def test_settings_field_info(self):
        """Test Settings field information and metadata"""
        fields = Settings.model_fields

        # Test that all expected fields exist
        expected_fields = [
            "calcom_api_key",
            "calcom_base_url",
            "mcp_server_name",
            "mcp_server_port",
            "mcp_server_host",
            "log_level",
        ]

        for field_name in expected_fields:
            assert field_name in fields

        # Test field types
        assert fields["calcom_api_key"].annotation == str
        assert fields["mcp_server_port"].annotation == int

    @patch.dict(os.environ, {}, clear=True)
    def test_settings_error_messages(self):
        """Test that Settings provides helpful error messages"""
        with patch.dict(os.environ, {}, clear=True):
            test_settings = Settings()
            # API key is now optional, so this should not raise
            assert hasattr(test_settings, "calcom_api_key")
            assert test_settings.calcom_api_key == ""

    def test_settings_repr_and_str(self):
        """Test Settings string representation"""
        test_settings = Settings(calcom_api_key="test-key")

        # Test that string representation doesn't expose sensitive data
        settings_str = str(test_settings)
        settings_repr = repr(test_settings)

        # Should contain class name
        assert "Settings" in settings_repr

        # API key might be partially hidden or shown - depends on Pydantic version
        # Just ensure the representation is reasonable
        assert len(settings_str) > 0
        assert len(settings_repr) > 0


if __name__ == "__main__":
    pytest.main([__file__])
