#!/usr/bin/env python3
"""
Cal.com MCP Server Entry Point
"""

import logging
from src.server import main as server_main
from src.config import settings


def main():
    """Main entry point for the Cal.com MCP server"""
    logging.basicConfig(
        level=getattr(logging, settings.log_level),
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    )

    logger = logging.getLogger(__name__)
    logger.info(f"Starting {settings.mcp_server_name}")
    logger.info(f"Cal.com API Base: {settings.calcom_base_url}")
    logger.info(
        f"Server will run on {getattr(settings, 'mcp_server_host', '0.0.0.0')}:{getattr(settings, 'mcp_server_port', 8000)}"
    )

    # Run the standard MCP server
    server_main()


if __name__ == "__main__":
    main()
